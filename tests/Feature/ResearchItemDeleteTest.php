<?php

namespace Tests\Feature;

use App\Livewire\CaseFiles\ResearchItems;
use App\Models\CaseFile;
use App\Models\LegalResearchItem;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Tests\TestCase;

class ResearchItemDeleteTest extends TestCase
{
    use RefreshDatabase;

    public function test_authorized_user_can_delete_research_item()
    {
        // Create a user and case file
        $user = User::factory()->create();
        $caseFile = CaseFile::factory()->create(['user_id' => $user->id]);
        
        // Create a research item
        $researchItem = LegalResearchItem::factory()->create([
            'case_file_id' => $caseFile->id,
            'title' => 'Test Research Item',
            'description' => 'Test description',
            'source_type' => 'case_law',
            'relevance_score' => 85,
        ]);

        // Act as the user and test the delete functionality
        $this->actingAs($user);

        Livewire::test(ResearchItems::class, ['caseFile' => $caseFile])
            ->call('deleteResearchItem', $researchItem)
            ->assertDispatched('notify', function ($event) {
                return $event['type'] === 'success' && 
                       str_contains($event['message'], 'Test Research Item') &&
                       str_contains($event['message'], 'deleted successfully');
            });

        // Assert the research item was deleted from the database
        $this->assertDatabaseMissing('legal_research_items', [
            'id' => $researchItem->id
        ]);
    }

    public function test_unauthorized_user_cannot_delete_research_item()
    {
        // Create two users
        $owner = User::factory()->create();
        $unauthorizedUser = User::factory()->create();
        
        // Create a case file owned by the first user
        $caseFile = CaseFile::factory()->create(['user_id' => $owner->id]);
        
        // Create a research item
        $researchItem = LegalResearchItem::factory()->create([
            'case_file_id' => $caseFile->id,
            'title' => 'Test Research Item',
        ]);

        // Act as the unauthorized user
        $this->actingAs($unauthorizedUser);

        // Attempt to delete should fail with authorization error
        $this->expectException(\Illuminate\Auth\Access\AuthorizationException::class);

        Livewire::test(ResearchItems::class, ['caseFile' => $caseFile])
            ->call('deleteResearchItem', $researchItem);

        // Assert the research item still exists
        $this->assertDatabaseHas('legal_research_items', [
            'id' => $researchItem->id
        ]);
    }

    public function test_delete_research_item_with_openai_document()
    {
        // Create a user and case file
        $user = User::factory()->create();
        $caseFile = CaseFile::factory()->create(['user_id' => $user->id]);
        
        // Create a research item with a document_id (simulating OpenAI integration)
        $researchItem = LegalResearchItem::factory()->create([
            'case_file_id' => $caseFile->id,
            'title' => 'Test Research Item with AI',
            'document_id' => 1, // Simulating an associated document
        ]);

        // Act as the user
        $this->actingAs($user);

        Livewire::test(ResearchItems::class, ['caseFile' => $caseFile])
            ->call('deleteResearchItem', $researchItem)
            ->assertDispatched('notify', function ($event) {
                return $event['type'] === 'success';
            });

        // Assert the research item was deleted
        $this->assertDatabaseMissing('legal_research_items', [
            'id' => $researchItem->id
        ]);
    }
}
