<?php

namespace Database\Factories;

use App\Models\CaseFile;
use App\Models\LegalResearchItem;
use Illuminate\Database\Eloquent\Factories\Factory;

class LegalResearchItemFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = LegalResearchItem::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $sourceTypes = ['case_law', 'statute', 'regulation', 'legal_article', 'legal_document', 'other'];
        $statuses = ['active', 'completed', 'archived'];
        $researchStatuses = ['not_started', 'queued', 'in_progress', 'completed', 'failed'];

        return [
            'case_file_id' => CaseFile::factory(),
            'title' => fake()->sentence(4),
            'description' => fake()->paragraph(2),
            'source_type' => fake()->randomElement($sourceTypes),
            'citation' => fake()->optional()->regexify('[A-Z]{2,4} [0-9]{1,4} \([0-9]{4}\)'),
            'content_data' => [
                'court' => fake()->optional()->words(2, true),
                'year' => fake()->optional()->year(),
                'jurisdiction' => fake()->optional()->state(),
            ],
            'relevance_analysis' => [
                'summary' => fake()->optional()->sentence(),
                'key_points' => fake()->optional()->words(5),
            ],
            'relevance_score' => fake()->numberBetween(1, 100),
            'is_favorited' => fake()->boolean(20), // 20% chance of being favorited
            'status' => fake()->randomElement($statuses),
            'research_status' => fake()->randomElement($researchStatuses),
            'research_id' => fake()->optional()->uuid(),
            'research_requested_at' => fake()->optional()->dateTimeBetween('-1 month', 'now'),
            'research_completed_at' => fake()->optional()->dateTimeBetween('-1 month', 'now'),
            'research_report_url' => fake()->optional()->url(),
            'research_error' => null,
            'research_markdown_content' => fake()->optional()->paragraphs(3, true),
            'research_local_path' => fake()->optional()->filePath(),
            'openai_file_id' => fake()->optional()->uuid(),
            'document_id' => null,
        ];
    }

    /**
     * Indicate that the research item is favorited.
     */
    public function favorited(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_favorited' => true,
        ]);
    }

    /**
     * Indicate that the research item has completed research.
     */
    public function withCompletedResearch(): static
    {
        return $this->state(fn (array $attributes) => [
            'research_status' => 'completed',
            'research_requested_at' => fake()->dateTimeBetween('-1 week', '-1 day'),
            'research_completed_at' => fake()->dateTimeBetween('-1 day', 'now'),
            'research_markdown_content' => fake()->paragraphs(5, true),
        ]);
    }

    /**
     * Indicate that the research item has failed research.
     */
    public function withFailedResearch(): static
    {
        return $this->state(fn (array $attributes) => [
            'research_status' => 'failed',
            'research_requested_at' => fake()->dateTimeBetween('-1 week', '-1 day'),
            'research_error' => fake()->sentence(),
        ]);
    }

    /**
     * Indicate that the research item has an associated OpenAI document.
     */
    public function withDocument(): static
    {
        return $this->state(fn (array $attributes) => [
            'document_id' => fake()->numberBetween(1, 100),
            'openai_file_id' => fake()->uuid(),
        ]);
    }
}
