<div class="space-y-6"
    x-data="{
        init() {
            Livewire.on('notify', (data) => {
                if (window.showNotification) {
                    window.showNotification(data[0].message, data[0].type);
                } else {
                    alert(data[0].message);
                }
            });
        }
    }">
    @if($insufficientCredits)
    <div class="alert alert-warning mb-4">
        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" /></svg>
        <div>
            <h3 class="font-bold">Insufficient Credits</h3>
            <div class="text-sm">You need at least {{ $requiredCredits }} credits to generate legal research items. Your current balance is {{ $currentBalance }} credits.</div>
            <a href="{{ route('credits.purchase.form') }}" class="btn btn-sm btn-outline mt-2">Purchase Credits</a>
        </div>
    </div>
    @endif
    <div class="flex items-center justify-between">
        <h2 class="text-xl font-semibold">{{ __('research.title') }}</h2>
        <div class="flex space-x-2">

            <button
                wire:click="generateItems"
                wire:loading.attr="disabled"
                class="px-4 py-2 text-white transition {{ $insufficientCredits ? 'bg-gray-500 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700' }} rounded-md"
                {{ count($items) > 0 ? 'title=' . __('research.generate_more_title') : '' }}
                {{ $insufficientCredits ? 'title="You need ' . $requiredCredits . ' credits to generate research items. Current balance: ' . $currentBalance . '"' : '' }}
            >
                <span wire:loading.remove wire:target="generateItems">
                    @if($insufficientCredits)
                        Need {{ $requiredCredits }} Credits
                    @else
                        {{ count($items) > 0 ? __('research.generate_more') : __('research.generate_items') }}
                    @endif
                </span>
                <span wire:loading wire:target="generateItems">
                    <svg class="inline w-4 h-4 mr-2 -ml-1 text-white animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    {{ __('research.processing') }}
                </span>
            </button>
        </div>
    </div>

    @if($errorMessage)
        <div class="p-4 mb-4 text-red-700 bg-red-100 border-l-4 border-red-500" role="alert">
            <p>{{ $errorMessage }}</p>
        </div>
    @endif

    @if(count($items) === 0)
        <div class="p-6 text-center rounded-lg bg-gray-50">
            <p class="text-gray-500">{{ __('research.no_items') }}</p>
        </div>
    @else
        <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
            @foreach($items as $item)
                <div class="bg-white rounded-lg shadow-md overflow-hidden border {{ $item->is_favorited ? 'border-yellow-400' : 'border-gray-200' }}">
                    <div class="p-4">
                        <div class="flex items-start justify-between">
                            <h3 class="text-lg font-medium text-gray-900">{{ $item->title }}</h3>
                            <div class="flex space-x-2">
                                <button wire:click="toggleFavorite({{ $item->id }})" class="text-gray-400 hover:text-yellow-500" title="{{ $item->is_favorited ? __('research.unfavorite') : __('research.favorite') }}">
                                    @if($item->is_favorited)
                                        <svg class="w-5 h-5 text-yellow-500" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                        </svg>
                                    @else
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                                        </svg>
                                    @endif
                                </button>

                                <button
                                    wire:click="deleteResearchItem({{ $item->id }})"
                                    wire:confirm="Are you sure you want to delete '{{ $item->title }}'? This action cannot be undone."
                                    class="text-gray-400 hover:text-red-500"
                                    title="{{ __('research.delete_item') }}"
                                >
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                </button>

                                <div class="relative" x-data="{ open: false }">
                                    <button @click="open = !open" class="text-gray-400 hover:text-gray-600" title="{{ __('research.more_options') }}">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0z"></path>
                                        </svg>
                                    </button>
                                    <div x-show="open" @click.away="open = false" class="absolute right-0 z-10 w-48 mt-2 bg-white rounded-md shadow-lg">
                                        <div class="py-1">
                                            <button wire:click="updateStatus({{ $item->id }}, 'active')" class="block w-full px-4 py-2 text-sm text-left text-gray-700 hover:bg-gray-100">
                                                {{ __('research.mark_active') }}
                                            </button>
                                            <button wire:click="updateStatus({{ $item->id }}, 'completed')" class="block w-full px-4 py-2 text-sm text-left text-gray-700 hover:bg-gray-100">
                                                {{ __('research.mark_completed') }}
                                            </button>
                                            <button wire:click="updateStatus({{ $item->id }}, 'archived')" class="block w-full px-4 py-2 text-sm text-left text-gray-700 hover:bg-gray-100">
                                                {{ __('research.archive') }}
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $item->status === 'active' ? 'bg-green-100 text-green-800' : ($item->status === 'completed' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800') }}">
                                {{ __('research.status.' . $item->status) }}
                            </span>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 ml-2">
                                {{ __('research.source_type.' . Str::snake($item->source_type)) }}
                            </span>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 ml-2">
                                {{ __('research.relevance') }}: {{ $item->relevance_score }}/100
                            </span>

                            @if($item->research_status !== 'not_started')
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ml-2
                                    {{ $item->research_status === 'completed' ? 'bg-green-100 text-green-800' :
                                       ($item->research_status === 'in_progress' ? 'bg-blue-100 text-blue-800' :
                                       ($item->research_status === 'queued' ? 'bg-yellow-100 text-yellow-800' :
                                       'bg-red-100 text-red-800')) }}">
                                    {{ __('research.research_status.' . $item->research_status) }}
                                </span>
                            @endif

                            @if($item->document_id)
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800 ml-2">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                    </svg>
                                    {{ __('research.ai_knowledge') }}
                                </span>
                                <button
                                    wire:click="deleteResearchFromOpenAI({{ $item->id }})"
                                    class="ml-2 text-xs text-red-600 hover:text-red-800"
                                    title="{{ __('research.remove_from_ai') }}"
                                >
                                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                </button>
                            @endif
                        </div>

                        <!-- Research Actions -->
                        <div class="flex justify-between mt-3">
                            <div>
                                @if($item->research_status === 'not_started')
                                    <button
                                        wire:click="initiateResearch({{ $item->id }})"
                                        class="px-3 py-1 text-xs text-white transition {{ $insufficientCredits ? 'bg-gray-500 cursor-not-allowed' : 'bg-blue-500 hover:bg-blue-600' }} rounded-md"
                                        {{ $insufficientCredits ? 'disabled title="You need ' . $requiredCredits . ' credits to start research. Current balance: ' . $currentBalance . '"' : '' }}
                                    >
                                        @if($insufficientCredits)
                                            Need {{ $requiredCredits }} Credits
                                        @else
                                            {{ __('research.actions.start_research') }}
                                        @endif
                                    </button>
                                @elseif($item->research_status === 'queued')
                                    <span class="flex items-center text-xs text-yellow-600">
                                        <svg class="w-3 h-3 mr-1 -ml-1 text-yellow-600 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        {{ __('research.research_status.queued') }}
                                    </span>
                                @elseif($item->research_status === 'in_progress')
                                    <span class="flex items-center text-xs text-blue-600">
                                        <svg class="w-3 h-3 mr-1 -ml-1 text-blue-600 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        {{ __('research.research_status.in_progress') }}
                                    </span>
                                @elseif($item->research_status === 'completed')
                                    <div class="flex space-x-2">
                                        <div class="flex space-x-1">

                                            @if($item->research_markdown_content)
                                                <button
                                                    wire:click="viewMarkdownContent({{ $item->id }})"
                                                    class="px-3 py-1 text-xs text-white transition bg-blue-500 rounded-md hover:bg-blue-600"
                                                >
                                                    {{ __('research.actions.view_summary') }}
                                                </button>
                                            @endif
                                        </div>
                                        <button
                                            wire:click="initiateResearch({{ $item->id }})"
                                            class="px-3 py-1 text-xs text-white transition {{ $insufficientCredits ? 'bg-gray-400 cursor-not-allowed' : 'bg-gray-500 hover:bg-gray-600' }} rounded-md"
                                            title="{{ $insufficientCredits ? 'You need ' . $requiredCredits . ' credits to research again. Current balance: ' . $currentBalance : __('research.actions.research_again_title') }}"
                                            {{ $insufficientCredits ? 'disabled' : '' }}
                                        >
                                            @if($insufficientCredits)
                                                Need Credits
                                            @else
                                                {{ __('research.actions.research_again') }}
                                            @endif
                                        </button>
                                    </div>
                                @elseif($item->research_status === 'failed')
                                    <div class="flex flex-col space-y-1">
                                        @if($item->research_error)
                                            <span class="text-xs text-red-600">{{ __('research.error_message', ['message' => Str::limit($item->research_error, 50)]) }}</span>
                                        @endif
                                        <button
                                            wire:click="retryResearch({{ $item->id }})"
                                            class="px-3 py-1 text-xs text-white transition {{ $insufficientCredits ? 'bg-gray-400 cursor-not-allowed' : 'bg-red-500 hover:bg-red-600' }} rounded-md"
                                            {{ $insufficientCredits ? 'disabled title="You need ' . $requiredCredits . ' credits to retry research. Current balance: ' . $currentBalance . '"' : '' }}
                                        >
                                            @if($insufficientCredits)
                                                Need {{ $requiredCredits }} Credits
                                            @else
                                                {{ __('research.actions.retry_research') }}
                                            @endif
                                        </button>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <p class="mt-3 text-gray-600">{{ $item->description }}</p>

                        @if($item->citation)
                            <div class="mt-3 text-sm text-gray-500">
                                <span class="font-medium">{{ __('research.metadata.citation') }}:</span> {{ $item->citation }}
                            </div>
                        @endif

                        @if(is_array($item->content_data) && !empty($item->content_data))
                            <div class="pt-3 mt-4 border-t">
                                <h4 class="text-sm font-medium text-gray-900">{{ __('research.metadata.additional_info') }}</h4>
                                <dl class="mt-2 text-sm">
                                    @foreach($item->content_data as $key => $value)
                                        <div class="mt-1">
                                            <dt class="inline font-medium text-gray-500">{{ __('research.metadata.field', ['field' => ucfirst(str_replace('_', ' ', $key))]) }}:</dt>
                                            <dd class="inline ml-1 text-gray-700">
                                                @if(is_array($value))
                                                    {{ implode(', ', $value) }}
                                                @else
                                                    {{ $value }}
                                                @endif
                                            </dd>
                                        </div>
                                    @endforeach
                                </dl>
                            </div>
                        @endif
                    </div>
                </div>
            @endforeach
        </div>
    @endif
</div>
